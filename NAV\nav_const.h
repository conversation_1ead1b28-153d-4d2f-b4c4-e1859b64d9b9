/***********************************************************************************
This file DEFINED all header files of Constant parmameter
Application using constant parmameter should include this file first.
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-2-4          | First Creation             |
|DengWei       |  2023-2-13         | Modify             |
 Add EPSON_G370 Parms
|-----------+---------------+-------------------|  
***********************************************************************************/

#ifndef __NAV_CONST_H__
#define __NAV_CONST_H__
//#include "NAV_MCU.h"

#ifndef PI
	#define PI        						   (3.1415926535897932384626433832795) 
#endif 

#ifndef WIE
	#define WIE								    7.2921151467e-5//������ת��ƽ�����ٶ�
#endif

#ifndef G0
	#define  G0						    		9.7803267714//�����������
#endif

#ifndef g0
	#define  g0						    		 9.80665 //ƽ����������
#endif

#ifndef RE_WGS84
#define RE_WGS84    							6378137.0           	/* earth semimajor axis (WGS84) (m) */
#endif

#ifndef FE_WGS84
#define FE_WGS84							    (1/298.257)			/* earth flattening (WGS84) */// ����
#endif

#define Rp_WGS84								((1-(FE_WGS84))*(RE_WGS84))						// �̰���
#define cs_2								   	(2.0/3.0)					    					// ˫��������ϵ��
#define E_WGS84									 sqrt(2*(FE_WGS84)-(FE_WGS84)*(FE_WGS84))			// ��һƫ����
#define EP_WGS84								(sqrt((RE_WGS84)*(RE_WGS84)-(Rp_WGS84)*(Rp_WGS84))/(Rp_WGS84))	// �ڶ�ƫ����

//#ifndef eps
//	#define eps                      		   2.2204e-16
//#endif

#define DEG2RAD								   (PI/180.0)                          // deg to rad 
#define RAD2DEG     						   (180.0/PI)                          // rad to deg  
#define MilliSec2Sec						   (1/1000)							// ms to s

#define  MPS2KMPH                              3.6                                //m/s to km/h
#define  KMPH2MPS								(1.0/MPS2KMPH)					  //km/h to m/s

#ifndef WIN32
#define SAMPLE_FREQ						       200	//��Ƭ������200
#else
#define SAMPLE_FREQ						       200//100//�����ʵ��������ò���ʱ��,������100 modify at 20231027          
#endif
#define SAMPLE_FREQ_GNSS					   5									//GNSSƵ��
#define SAMPLE_FREQ_UWB					   10	//UWBƵ��					
#define MAX_IMU_BUFF 							20
//#define WHEEL_BUFFER_SIZE 							20 //���ٻ�����
#define SAMPLE_Ratio						   (SAMPLE_FREQ/SAMPLE_FREQ_GNSS)        //��Ҫ���ǣ�SAMPLE_Ratio ? SAMPLE_FREQ or SAMPLE_FREQ/SAMPLE_FREQ_GNSS
#define TS									   (1.0/SAMPLE_FREQ)			            // ���ݲ���

#define NA										15 //״̬������ά��

#define SINS_BUFFER_SIZE						15//�����SINS buffer��С
#define UP_SIZE						    20
#define ZUPT_SIZE						100//ZUPT����buffer��С
#define SOURCE_UP_dTim_ms						  60//(125)  //*****����ODO����Ƶ��************
// 改进的ZUPT检测阈值 - 多级检测 (调整为更敏感的检测)
#define TH_acc_STRICT				(2*0.542*0.001)//严格ZUPT检测的加表阈值...g (完全静止) - 降低阈值提高敏感度
#define TH_acc_LOOSE				(6*0.542*0.001)//宽松ZUPT检测的加表阈值...g (准静止) - 降低阈值提高敏感度
#define TH_gyr_STRICT				(2*0.013)//严格ZUPT检测的陀螺阈值...deg/s - 降低阈值提高敏感度
#define TH_gyr_LOOSE				(8*0.013)//宽松ZUPT检测的陀螺阈值...deg/s - 降低阈值提高敏感度

// 兼容性定义
#define TH_acc						TH_acc_STRICT
#define TH_gyr						TH_gyr_STRICT

// ZUPT状态级别定义
#define ZUPT_LEVEL_NONE				0    // 运动状态
#define ZUPT_LEVEL_QUASI_STATIC		1    // 准静止状态
#define ZUPT_LEVEL_STATIC			2    // 完全静止状态
#define KF_Stable_Size               75//*******����KF�����ж�******Լ15s*******
#define Check_OK_Cnt                     7//5*******ͨ������Ĵ���******
// 彻底修复航向角约束触发问题 - 在标定状态下完全禁用
#define ZUPT_DampingHeading_Size_MIN    1000//最小触发周期 - 200秒（基本不触发）
#define ZUPT_DampingHeading_Size_MAX    2000//最大触发周期 - 400秒
#define ZUPT_DampingHeading_Size        ZUPT_DampingHeading_Size_MIN//兼容性定义
#define subkf_fog_size                   40//300//60s
//*******OUTAGE SIMULATE****
#define outagetime    (2.0*60.0)
#define spantime  (2*60.0)
#define outnum  15

//���Ƶ�ǰ�豸����ģ��������λ����ʱ�䵥λs
#define MAX_WHEEL_MOEDL_DURATION			   100//120	
#define MAX_MOTION_MOEDL_DURATION		   		0//30
#define TGB										1000//���������ʱ��
#define TAB										1000//�Ӽ������ʱ��
//grs80����ģ�Ͳ���
//����beta=5.30240e-3
//beta1=5.82e-6
//beta2=2u/(R*R*R)=3.086e-6
//beta3=8.08e-9
#define Grs80_Beta0							5.30240e-3//ԭ�����е�beta
#define Grs80_Beta1							5.82e-6
#define Grs80_Beta2							3.086e-6
#define Grs80_Beta3							8.08e-9	





#define DEG									   ((PI)/180.0)						// �Ƕ�ת����
#define DPH									   ((DEG)/3600.0)
#define DPS									   ((DEG)/1.0)
#define UG									   ((G0)/1.0e6)
#define MG									   ((G0)/1.0e3)

#define DPSH								   ((DEG)/60)						    // rad/(sqrt(hour))
#define UGPSHZ								   ((UG)/1.0)						    // ug/(sqrt(Hz))
#define MGPSH                                  ((MG)/60)


//----------����Q�� ��Ҫ����ʵ�ʹߵ��豸ʵ���������--------------------------------------------------------------


/*��׼IMU460
������:
����-400��/s +400��/s
��ƫ���ȶ���4��/h
��ʼ��ƫ���0.1��/s
�������0.3��/��hr
���ٶȼ�:
���� X, Y, Z -6g +6g g=9.80665 m/s2
��ƫ���ȶ��� 10��g
��ʼ��ƫ��� 4mg
������� 0.05m/s/��hr
*/
//���ݳ�ʼ��ƫ��������ƫP����ĳ�ʼ��
#define IMU460_GYROBIAS                         (0.05*(DPS))//(0.02*(DPS))//(0.1*(DPS)) //(360*(DPH))				// ���ݳ�ʼ��ƫ(deg/h)  10    (0.1deg/s)
#define IMU460_GYROBIAS2                        (IMU460_GYROBIAS*IMU460_GYROBIAS)
//���ٶȼƳ�ʼ��ƫ��������ƫP����ĳ�ʼ��
#define IMU460_ACCBIAS                          (2*(MG))//(5*(MG))	//(4000*(UG))				// ���ٶȼƳ�ʼ��ƫ(ug)  1000  4000
#define IMU460_ACCBIAS2                         (IMU460_ACCBIAS*IMU460_ACCBIAS)
//0.3������λDPSH,��460�Ƕ�������߶��룬������̬���
#define IMU460_ANGNRANDOMWALK                   (0.5*(DPSH))//(0.38*(DPSH))	//(0.3*(DPSH))			// �Ƕ��������(deg/sqrt(h))//0.6,
#define IMU460_ANGNRANDOMWALK2                  (IMU460_ANGNRANDOMWALK*IMU460_ANGNRANDOMWALK)//��λrad*rad/s
//3.5����MGPSH��λ����m/s/sqrt(h)ת����ϵΪ3.5/1000*G0 ��460������߶��룬�����ٶ����
#define IMU460_VELRANDOMWALK                    (72*MGPSH)//(3.5*MGPSH)//(5*MGPSH)//(85*(UGPSHZ)*10)			// �ٶ��������(ug/sqrt(hz))=>3.5
#define IMU460_VELRANDOMWALK2                   (IMU460_VELRANDOMWALK*IMU460_VELRANDOMWALK)//��λ 
#define IMU460_GYROBIASSTABILITY               (10*(DPH))//(1*(DPH))// (50*(DPH))//(1*(DPH))//(0.1*(DPS)) //(360*(DPH))8				// ������ƫ(deg/h)  10    (0.1deg/s)  revised by WFat 20231128
//#define SCHA634_GYROBIAS                      (0.8*(DPH))//(0.1*(DPS)) //(360*(DPH))				// ������ƫ(deg/h)  10    (0.1deg/s)
#define IMU460_GYROBIASSTABILITY2              (IMU460_GYROBIASSTABILITY*IMU460_GYROBIASSTABILITY)
#define IMU460_ACCBIASSTABILITY                (0.2*(MG))// 400//(400*3*(UG))	//400//(4000*(UG))				// ���ٶȼ���ƫ(ug)  1000  4000
#define IMU460_ACCBIASSTABILITY2               (IMU460_ACCBIASSTABILITY*IMU460_ACCBIASSTABILITY)
#define IMU460_GYROSCALE						(0.01)//�����������P��ֵ0.01
#define IMU460_GYROSCALE2						(IMU460_GYROSCALE*IMU460_GYROSCALE)
/*����SCHA63T-K03
������:
����-300��/s +300��/s
��ƫ�ȶ���1.64��/h
��ʼ��ƫ���0.15��/s
�������0.09��/��hr
���ٶȼ�:
���� X, Y, Z -6g +6g
��ƫ�ȶ��� 12.2��g
��ʼ��ƫ��� 6.3mg
������� 0.035m/s/��hr
*/
#define SCHA634_GYROBIAS                        (0.3*(DPS))//(0.05*(DPS))//(1*(DPH))// (50*(DPH))//(1*(DPH))//(0.1*(DPS)) //(360*(DPH))8				// ������ƫ(deg/h)  10    (0.1deg/s)  revised by WFat 20231128
//#define SCHA634_GYROBIAS                      (0.8*(DPH))//(0.1*(DPS)) //(360*(DPH))				// ������ƫ(deg/h)  10    (0.1deg/s)
#define SCHA634_GYROBIAS2                       (SCHA634_GYROBIAS*SCHA634_GYROBIAS)
#define SCHA634_ACCBIAS                         (10.0*(MG))// 400//(400*3*(UG))	//400//(4000*(UG))				// ���ٶȼ���ƫ(ug)  1000  4000
#define SCHA634_ACCBIAS2                        (SCHA634_ACCBIAS*SCHA634_ACCBIAS)
//#define SCHA634_ANGNRANDOMWALK                (0.3*(DPSH))//(0.08*(DPSH))			// �Ƕ��������(deg/sqrt(h))//0.6
#define SCHA634_ANGNRANDOMWALK                  (5*0.1*(DPSH))//(1.0*(DPSH))//(0.3*(DPSH))//(0.3*5*(DPSH))//(0.15*(DPSH))			// �Ƕ��������(deg/sqrt(h))//0.6
#define SCHA634_ANGNRANDOMWALK2                 (SCHA634_ANGNRANDOMWALK*SCHA634_ANGNRANDOMWALK)
//#define SCHA634_VELRANDOMWALK                 (8*MGPSH)//(85*(UGPSHZ)*10)			// �ٶ��������(ug/sqrt(hz))
#define SCHA634_VELRANDOMWALK                   (13*4.2*MGPSH)//(8*9*MGPSH)//(30*(UGPSHZ)*10)			// �ٶ��������(ug/sqrt(hz))
#define SCHA634_VELRANDOMWALK2                  (SCHA634_VELRANDOMWALK*SCHA634_VELRANDOMWALK)
#define SCHA634_GYROBIASSTABILITY               (0.001*3.0*(DPH))//(1*(DPH))// (50*(DPH))//(1*(DPH))//(0.1*(DPS)) //(360*(DPH))8				// ������ƫ(deg/h)  10    (0.1deg/s)  revised by WFat 20231128
//#define SCHA634_GYROBIAS                      (0.8*(DPH))//(0.1*(DPS)) //(360*(DPH))				// ������ƫ(deg/h)  10    (0.1deg/s)
#define SCHA634_GYROBIASSTABILITY2              (SCHA634_GYROBIASSTABILITY*SCHA634_GYROBIASSTABILITY)
#define SCHA634_ACCBIASSTABILITY                (0.001*0.03*(MG))// 400//(400*3*(UG))	//400//(4000*(UG))				// ���ٶȼ���ƫ(ug)  1000  4000
#define SCHA634_ACCBIASSTABILITY2               (SCHA634_ACCBIASSTABILITY*SCHA634_ACCBIASSTABILITY)
#define SCHA634_GYROSCALE						(0.005)//���ݱ������ӳ�ʼ���
#define SCHA634_GYROSCALE2						(SCHA634_GYROSCALE*SCHA634_GYROSCALE)//���ݱ����������
/*ADIS16465-2
������:
����-500��/s +500��/s
��ƫ�ȶ���2.5��/h
��ʼ��ƫ���0.08��/s
�������0.15��/��hr
���ٶȼ�:
���� X, Y, Z -8g +8g
��ƫ�ȶ��� 3.6��g
��ʼ��ƫ��� 0.6mg
������� 0.012m/s/��hr
*/
#define ADIS16465_GYROBIAS                        (0.02*(DPS))//(0.10*(DPS)) //(360*(DPH))				// ������ƫ(deg/h)  10    (0.1deg/s)
#define ADIS16465_GYROBIAS2                       (ADIS16465_GYROBIAS*ADIS16465_GYROBIAS)
#define ADIS16465_ACCBIAS                         (10*(MG))//(10*(MG))//(10*(MG))	//(4000*(UG))				// ���ٶȼ���ƫ(ug)  1000  4000
#define ADIS16465_ACCBIAS2                        (ADIS16465_ACCBIAS*ADIS16465_ACCBIAS)
#define ADIS16465_ANGNRANDOMWALK                  (0.1*(DPSH))//(0.3*(DPSH))	//(0.09*(DPSH))			// �Ƕ��������(deg/sqrt(h))//0.6
#define ADIS16465_ANGNRANDOMWALK2                 (ADIS16465_ANGNRANDOMWALK*ADIS16465_ANGNRANDOMWALK)
#define ADIS16465_VELRANDOMWALK                   (10*MGPSH)//(10*MGPSH)//(10*MGPSH)//(85*(UGPSHZ)*10)			// �ٶ��������(ug/sqrt(hz))
#define ADIS16465_VELRANDOMWALK2                  (ADIS16465_VELRANDOMWALK*ADIS16465_VELRANDOMWALK)

/*EPSON_G370
������:
����-450��/s +450��/s
��ƫ���ȶ���0.8��/h
��ʼ��ƫ���0.1��/s
�������0.06��/��hr
���ٶȼ�:
���� X, Y, Z -10g +10g
��ƫ���ȶ��� 12��g
��ʼ��ƫ��� 2mg
������� 0.025m/s/��hr
*/
#define EPSON_G370_GYROBIAS                        (0.065*(DPS)) //(360*(DPH))				// ������ƫ(deg/h)  10    (0.1deg/s)
#define EPSON_G370_GYROBIAS2                       (EPSON_G370_GYROBIAS*EPSON_G370_GYROBIAS)
#define EPSON_G370_ACCBIAS                         (10*(MG))	//(4000*(UG))				// ���ٶȼ���ƫ(ug)  1000  4000
#define EPSON_G370_ACCBIAS2                        (EPSON_G370_ACCBIAS*EPSON_G370_ACCBIAS)
#define EPSON_G370_ANGNRANDOMWALK                  (0.3*(DPSH))			// �Ƕ��������(deg/sqrt(h))//0.6
#define EPSON_G370_ANGNRANDOMWALK2                 (EPSON_G370_ANGNRANDOMWALK*EPSON_G370_ANGNRANDOMWALK)
#define EPSON_G370_VELRANDOMWALK                   (11*MGPSH)//(85*(UGPSHZ)*10)			// �ٶ��������(ug/sqrt(hz))
#define EPSON_G370_VELRANDOMWALK2                  (EPSON_G370_VELRANDOMWALK*EPSON_G370_VELRANDOMWALK)

//fog_5001//ʵ��ʹ��fog60****************
#define FOG_GYROBIAS                            (0.05*DPS)//(3*(DPH))//(0.6*10*(DPH))//(0.6*45*(DPH))//			// ������ƫ(deg/h)  10    (0.1deg/s)
#define FOG_GYROBIAS2                           (FOG_GYROBIAS*FOG_GYROBIAS)
#define FOG_GYROBIASSTABILITY                   (0.01*5.0e-4*DPH)//fog60****0.1~0.3*******
#define FOG_GYROBIASSTABILITY2                  (FOG_GYROBIASSTABILITY*FOG_GYROBIASSTABILITY)
#define FOG_ANGNRANDOMWALK                      (2000*2.0e-4*DPSH)//(0.45*(DPSH))	//(0.07*3*(DPSH)) //(0.2*(DPSH))(fog50:0.01--fog70:0.004)		
#define FOG_ANGNRANDOMWALK2                     (FOG_ANGNRANDOMWALK*FOG_ANGNRANDOMWALK)

//
#define LEVER_VAR							1.0						// �˱�����
//״̬����λ����Ŀǰ��Ϊλ�����Զ����GNSS���㶨λ���Ժ���Խ�ģ����
#define  INS_POS_VAR						(((5.0/RE_WGS84)*(5.0/RE_WGS84)) *9.0)         // : ��Ҫ���Կ���3������Ƿ����
#define  INS_HEAD_VAR						(5.5*5.5*9.0)//


//-----------������̬���ٶȺ�λ�ö�Ӧ��P��-------------------------------------------------------------
#define DATT								(3.0*(DEG))//(1*(DEG))
#define DATT_VAR							(DATT*DATT)
#define DATT2								(3.0*(DEG))//(5*(DEG))
#define DATT2_VAR							(DATT2*DATT2)

#define DVEL									0.5//1.0
#define DVEL_VAR							(DVEL*DVEL)

#define DPOS								(1.0/RE_WGS84) //(1/RE_WGS84)         //����λ��״̬ȡ�����꣨��γ�ߣ�ʱ
#define DPOS_VAR							(DPOS*DPOS)
#define DPOS2								(1.0)            //����λ��״̬ȡֱ������ʱ
#define DPOS2_VAR							(DPOS2*DPOS2)
//----------����R��--------------------------------------------------------------
/*************************************************GNSS R��������*************************************************************************/
//�������������δ�̶��������ǳ���,�������������йأ����ﶨ��1m���ߺ������
#define HEAD_VAR_OFF						((10000000.0*DEG)*(10000000.0*DEG))
#define RTK_HEAD_VAR						((0.2*DEG)*(0.2*DEG))//((0.27*DEG)*(0.27*DEG))//((0.4*DEG)*(0.4*DEG))//((0.2*DEG)*(0.2*DEG))//
#define RTK_TURN_HEAD_VAR					((90.0*DEG)*(90.0*DEG))//ת���ʱ�������,��Ϊʱ�����⣬��׼
#define NO_RTK_HEAD_VAR						((0.6*DEG)*(0.6*DEG))//û��RTK�Ŀ��������£�˫���߾���******
//UM982�ٶ����0.03m/s,rtk���rtkһ��
#define VEL_VAR								(0.04)//(0.05)
#define VEL_VAR2							(VEL_VAR*VEL_VAR)//(0.05*0.05)//
//#define RTK_VEL_VAR						(0.05*0.05)
//#define SPP_VEL_VAR                        (0.1*0.1)
//#define VEL_VAR_F							(2.2/SAMPLE_Ratio)*(2.2/SAMPLE_Ratio)			//�ٶ������ʱ���й�ϵ��0.2m/s

//λ�������þ�γ�߳�
//���㶨λ��α���ּ�������ʱʹ��һ�����,
#define SPP_POS_VAR						 1.5//5.0//1.5
//��ֽ�
#define DGPS_POS_VAR					 0.5//1.0
//�߳����
#define SPP_POS_HEIGHT_VAR2				((SPP_POS_VAR*1.7)*(SPP_POS_VAR*1.7))
//��γ�����
#define SPP_POS_LON_LAT_VAR2				((SPP_POS_VAR/RE_WGS84)*(SPP_POS_VAR/RE_WGS84))
//��ֽ�
#define DGPS_POS_HEIGHT_VAR2				((DGPS_POS_VAR*2.0)*(DGPS_POS_VAR*2.0))
//��γ�����
#define DGPS_POS_LON_LAT_VAR2				((DGPS_POS_VAR/RE_WGS84)*(DGPS_POS_VAR/RE_WGS84))

#define RTK_POS_VAR						0.03//0.03//0.008//0.05
//rtk�߳����
#define RTK_POS_HEIGHT_VAR2			    (RTK_POS_VAR*RTK_POS_VAR)//((RTK_POS_VAR*2.0)*(RTK_POS_VAR*2.0))//
//rtk��γ�����
#define RTK_POS_LON_LAT_VAR2				  ((RTK_POS_VAR/RE_WGS84/2.0)*(RTK_POS_VAR/RE_WGS84/2.0))//((RTK_POS_VAR/RE_WGS84)*(RTK_POS_VAR/RE_WGS84))

/*************************************************ODSMETER R��������*************************************************************************/
//��̼��������Χ
#define  MAX_WHEEL_VEL                            (300.0*KMPH2MPS)
//��̼��������
#define WHEEL_VEL_VAR					(0.06*0.06)///(0.2*0.2)*****NHC****
#define WHEEL_VEL_VAR2					(0.05*0.05)//(0.08*0.08)//(0.2*0.2)//*******Ŀǰ�޷����ODO�����ֲᣬֻ�ܽ��г���****

/****************************************************UWB R��������*************************************************************************/


#define AHRS_DRIFT_PR_P   (0.00025817*2)
#define AHRS_DRIFT_PR_I   (0.00003)


#define fog_dk_filte (-0.0775f)//5001

#endif
