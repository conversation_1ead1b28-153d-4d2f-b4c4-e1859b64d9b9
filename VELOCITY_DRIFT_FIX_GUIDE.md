# 北向速度发散问题修复指南

## 问题描述
**严重问题**：从运动到静止时，北向速度不仅没有收敛到0，反而持续增大！
- 静止状态下北向速度发散（-0.259399 m/s）
- 预期2秒内速度应接近0，但实际速度持续增大
- ZUPT检测延迟导致速度积分继续累积误差

## 根本原因分析

1. **ZUPT检测延迟**：需要100个样本窗口（0.5秒@200Hz）才能检测到静止
2. **速度积分持续**：在ZUPT检测期间，速度积分仍在进行，累积误差
3. **约束强度不足**：现有ZUPT约束不够强，无法快速抑制速度发散
4. **缺乏预防机制**：没有在速度更新前进行预防性检查

## 修复方案（多层防护）

### 1. 降低ZUPT检测阈值（提高敏感度）
**文件：** `NAV/nav_const.h`

```c
// 修改前：
#define TH_acc_STRICT    (3*0.542*0.001)  // 严格阈值
#define TH_gyr_STRICT    (3*0.013)

// 修改后：
#define TH_acc_STRICT    (2*0.542*0.001)  // 降低阈值，提高检测敏感度
#define TH_gyr_STRICT    (2*0.013)
```

### 2. 增强ZUPT速度约束强度
**文件：** `NAV/nav_kf.c`

```c
// 修改前：
#define ZUPTstd    (0.02)  // 观测噪声较大

// 修改后：
#define ZUPTstd    (0.005) // 降低观测噪声，提供更强约束
```

### 3. 添加完全静止状态的强制速度约束
**文件：** `NAV/nav_kf.c`

```c
// 在ZUPT观测更新中添加：
if (NAV_Data_Full_p->ZUPT_level == ZUPT_LEVEL_STATIC) {
    // 完全静止状态：直接将速度设为0
    NAV_Data_Full_p->SINS.vn[0] = 0.0;
    NAV_Data_Full_p->SINS.vn[1] = 0.0; 
    NAV_Data_Full_p->SINS.vn[2] = 0.0;
    // 使用极小的观测噪声
    R_ZUPT[0] = R_ZUPT[1] = R_ZUPT[2] = (0.001*0.001);
}
```

### 4. 改进过程噪声动态调整
**文件：** `NAV/nav_kf.c`

```c
// 在静止状态下大幅减小速度过程噪声：
if (NAV_Data_Full_p->ZUPT_level == ZUPT_LEVEL_STATIC) {
    velocity_reduction_factor = 0.01;  // 减小到1%
} else if (NAV_Data_Full_p->ZUPT_level == ZUPT_LEVEL_QUASI_STATIC) {
    velocity_reduction_factor = 0.1;   // 减小到10%
}
```

### 5. 添加快速ZUPT检测机制
**文件：** `NAV/nav_imu.c`

```c
// 即使窗口未满，也进行实时检测
if (!NAV_Data_Full_p->ins_buffer_full_flag || NAV_Data_Full_p->ZUPT_flag == RETURN_FAIL) {
    double current_acc_mag = sqrt(...);  // 当前加速度幅值
    double current_gyr_mag = sqrt(...);  // 当前陀螺仪幅值

    if (current_acc_mag < 0.05 && current_gyr_mag < 0.5*DEG2RAD) {
        NAV_Data_Full_p->ZUPT_flag = RETURN_SUCESS;
        NAV_Data_Full_p->ZUPT_level = ZUPT_LEVEL_STATIC;
    }
}
```

### 6. 速度更新前的预防性约束
**文件：** `NAV/nav_sins.c`

```c
// 在SINS_UP_HP和SINS_Update函数中添加：
double vel_mag_before = sqrt(vn1[0]*vn1[0] + vn1[1]*vn1[1] + vn1[2]*vn1[2]);
double acc_mag_check = sqrt(...);  // IMU加速度检查
double gyr_mag_check = sqrt(...);  // IMU陀螺仪检查

// 如果IMU显示静止但速度很大，进行预防性约束
if (acc_mag_check < 0.1 && gyr_mag_check < 0.1*DEG2RAD && vel_mag_before > 0.2) {
    double emergency_damping = 0.9;
    vn1[0] *= emergency_damping;  // 强制衰减速度
    vn1[1] *= emergency_damping;
    vn1[2] *= emergency_damping;
}
```

### 7. 多层速度发散监控
**文件：** `NAV/nav_kf.c`

```c
// 激进的速度约束方案：
if (NAV_Data_Full_p->ZUPT_flag == RETURN_SUCESS) {
    // ZUPT已触发：立即强制约束
    if (NAV_Data_Full_p->ZUPT_level == ZUPT_LEVEL_STATIC) {
        NAV_Data_Full_p->SINS.vn[0] = 0.0;
        NAV_Data_Full_p->SINS.vn[1] = 0.0;
        NAV_Data_Full_p->SINS.vn[2] = 0.0;
    }
} else {
    // ZUPT未触发但速度异常：预防性约束
    if (vel_magnitude > 0.5) {
        // 检查IMU是否表明静止
        if (acc_magnitude < 0.1 && gyr_magnitude < 0.1*DEG2RAD) {
            // 紧急衰减
            NAV_Data_Full_p->SINS.vn[0] *= 0.95;
            NAV_Data_Full_p->SINS.vn[1] *= 0.95;
            NAV_Data_Full_p->SINS.vn[2] *= 0.95;
        }
    }
}
```

## 测试验证方案

### 1. 静止状态测试
1. **测试条件**：车辆完全静止15分钟
2. **监控指标**：
   - 北向速度发散率：目标 < 0.01 m/s/min
   - ZUPT检测频率：应保持高频触发
   - ZUPT级别分布：应主要为ZUPT_LEVEL_STATIC

### 2. 关键监控参数
```c
// 在调试输出中添加：
printf("ZUPT: flag=%d, level=%d, vel_N=%.6f\n", 
       ZUPT_flag, ZUPT_level, SINS.vn[1]);
printf("Vel_mag=%.6f, R_ZUPT=%.9f\n", 
       vel_magnitude, R_ZUPT[0]);
```

### 3. 预期效果
- **北向速度**：从-0.259 m/s 改善到 < ±0.01 m/s
- **速度发散率**：从当前水平降低到 < 0.01 m/s/min
- **ZUPT触发**：在静止状态下保持稳定触发

## 参数调优建议

### 如果效果不理想，可进一步调整：

1. **更严格的ZUPT阈值**：
```c
#define TH_acc_STRICT    (1.5*0.542*0.001)
#define TH_gyr_STRICT    (1.5*0.013)
```

2. **更强的速度约束**：
```c
#define ZUPTstd    (0.001)  // 进一步降低观测噪声
```

3. **更频繁的强制约束**：
```c
if (vel_magnitude > 0.05) {  // 降低触发阈值
    // 强制约束逻辑
}
```

## 注意事项

1. **避免过度约束**：在车辆开始运动时，确保ZUPT能够及时退出
2. **监控副作用**：观察是否影响正常行驶时的速度估计精度
3. **渐进式调整**：建议先测试当前修改，根据效果再进一步调优

## 编译和部署

1. 使用Keil uVision打开项目文件 `Project/INS.uvprojx`
2. 编译项目检查是否有错误
3. 烧录到设备进行测试
4. 通过串口或CAN总线监控输出数据

修复后的系统应该能够在静止状态下将北向速度控制在±0.01 m/s以内。
