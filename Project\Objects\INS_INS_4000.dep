Dependencies for Project 'INS', Target 'INS_4000': (DO NOT MODIFY !)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_adc.c)(0x6229735A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_adc.o --omf_browse .\objects\gd32f4xx_adc.crf --depend .\objects\gd32f4xx_adc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_can.c)(0x6507C602)()
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_crc.c)(0x6229735A)()
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_ctc.c)(0x6229735A)()
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dac.c)(0x6229735A)()
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dbg.c)(0x6229735A)()
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dci.c)(0x6229735A)()
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dma.c)(0x6229735A)()
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_enet.c)(0x6229735A)()
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_exmc.c)(0x6229735A)()
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_exti.c)(0x6229735A)()
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_fmc.c)(0x6229735A)()
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_fwdgt.c)(0x6229735A)()
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_gpio.c)(0x6481A692)()
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_i2c.c)(0x6229735A)()
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_ipa.c)(0x6229735A)()
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_iref.c)(0x6229735A)()
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_misc.c)(0x6229735A)()
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_pmu.c)(0x6229735A)()
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_rcu.c)(0x6229735A)()
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_rtc.c)(0x6229735A)()
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_sdio.c)(0x6229735A)()
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_spi.c)(0x6229735A)()
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_syscfg.c)(0x6229735A)()
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_timer.c)(0x6229735A)()
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_tli.c)(0x6229735A)()
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_trng.c)(0x6229735A)()
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_usart.c)(0x6229735A)()
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_wwdgt.c)(0x6229735A)()
F (..\Source\src\gd32f4xx_it.c)(0x683E4FDA)()
F (..\Source\src\main.c)(0x684FDB9B)()
F (..\Source\src\systick.c)(0x62AAE42C)()
F (..\Source\src\can_data.c)(0x64780368)()
F (..\Source\src\gnss.c)(0x6561981E)()
F (..\Source\src\imu_data.c)(0x6256891A)()
F (..\Source\src\INS_Data.c)(0x64FFD01A)()
F (..\Source\src\INS_Sys.c)(0x6497EC52)()
F (..\Source\src\TCPServer.c)(0x62CE2422)()
F (..\Source\src\Time_Unify.c)(0x62C3A0A2)()
F (..\Source\src\fpgad.c)(0x683C1BF6)()
F (..\Source\src\ymodem.c)(0x64C1D8B8)()
F (..\Source\src\clock.c)(0x63B3A700)()
F (..\Source\src\Data_shift.c)(0x628B215E)()
F (..\Source\src\gdwatch.c)(0x6566A78A)()
F (..\Source\src\INS_Output.c)(0x675AAFE6)()
F (..\Source\src\FirmwareUpdateFile.c)(0x683E5B98)()
F (..\bsp\src\bmp2.c)(0x62A3063A)()
F (..\bsp\src\bmp280.c)(0x62C4FB5C)()
F (..\bsp\src\bsp_adc.c)(0x628B6228)()
F (..\bsp\src\bsp_can.c)(0x668E44FA)()
F (..\bsp\src\bsp_exti.c)(0x67176CCC)()
F (..\bsp\src\bsp_flash.c)(0x6497F2D8)()
F (..\bsp\src\bsp_fmc.c)(0x656718AC)()
F (..\bsp\src\bsp_fwdgt.c)(0x62CD2532)()
F (..\bsp\src\bsp_gpio.c)(0x683E6154)()
F (..\bsp\src\bsp_rtc.c)(0x62A9516C)()
F (..\bsp\src\bsp_sys.c)(0x6264C948)()
F (..\bsp\src\bsp_tim.c)(0x65000B52)()
F (..\bsp\src\bsp_uart.c)(0x683C1E5A)()
F (..\bsp\src\CH378_HAL.C)(0x62958CE2)()
F (..\bsp\src\CH395CMD.C)(0x62CCD952)()
F (..\bsp\src\CH395SPI.C)(0x62CD237A)()
F (..\bsp\src\common.c)(0x62C52124)()
F (..\bsp\src\FILE_SYS.C)(0x62BED492)()
F (..\bsp\src\Logger.c)(0x62CBD092)()
F (..\bsp\src\TCP_Server.c)(0x6295E26C)()
F (..\bsp\src\CH378_SPI_HW.C)(0x62CBD0BC)()
F (..\bsp\src\bsp_soft_i2c_master.c)(0x62C4FA22)()
F (..\Library\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s)(0x64A90C06)()
F (..\Library\CMSIS\GD\GD32F4xx\Source\system_gd32f4xx.c)(0x62A8454C)()
F (..\Library\CMSIS\arm_cortexM4lf_math.lib)(0x581CC65E)()
F (..\Common\src\data_convert.c)(0x62A853B6)()
F (..\RTT\SEGGER_RTT.c)(0x60DDE3EC)()
F (..\RTT\SEGGER_RTT_printf.c)(0x60DDE3EC)()
F (..\NAV\nav.c)(0x67839870)()
F (..\NAV\nav_app.c)(0x67FCA050)()
F (..\NAV\nav_kf.c)(0x6838328A)()
F (..\NAV\nav_math.c)(0x66AAF5AE)()
F (..\NAV\nav_ods.c)(0x67A951E8)()
F (..\NAV\nav_sins.c)(0x67579D30)()
F (..\NAV\navlog.c)(0x683E5BE2)()
F (..\NAV\nav_cli.c)(0x672D875C)()
F (..\NAV\nav_gnss.c)(0x671225C4)()
F (..\NAV\nav_imu.c)(0x67FC9FC2)()
F (..\NAV\nav_magnet.c)(0x64A4C2AE)()
F (..\NAV\nav_mahony.c)(0x64F83D2A)()
F (..\NAV\nav_uwb.c)(0x64A4C2AE)()
F (..\Protocol\computerFrameParse.c)(0x683E683C)()
F (..\Protocol\frame_analysis.c)(0x683E5E72)()
F (..\Protocol\protocol.c)(0x683E68C4)()
F (..\Protocol\transplant.c)(0x64784FD8)()
F (..\Protocol\SetParaBao.c)(0x683E5EA2)()
