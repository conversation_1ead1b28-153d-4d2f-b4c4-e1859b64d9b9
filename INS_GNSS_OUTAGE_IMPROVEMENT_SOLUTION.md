# INS设备GNSS失锁后北向速度和航向角发散问题改进方案

## 问题分析总结

### 1. 根本原因分析

通过对代码的深入分析，发现GNSS失锁后北向速度和航向角发散的主要原因包括：

1. **ZUPT检测阈值过于严格**
   - 加速度计阈值：0.00271g (约0.027 m/s²)
   - 陀螺仪阈值：0.065 deg/s
   - 在实际应用中很难满足，导致ZUPT触发频率低

2. **ZUPT约束机制不完善**
   - ZUPT更新只约束速度状态（索引3-5），不直接约束航向角状态（索引2）
   - 航向角在静止状态下仍然依赖陀螺仪积分，容易发散

3. **航向角约束触发条件过于严格**
   - 需要连续60个周期的ZUPT状态才能触发航向角约束
   - 实际应用中很难满足如此长时间的连续静止条件

4. **过程噪声矩阵设置不当**
   - 在GNSS失锁期间，过程噪声持续累积
   - 没有根据ZUPT状态动态调整噪声参数

### 2. 改进方案实施

#### 2.1 多级ZUPT检测算法

**文件修改：** `NAV/nav_const.h`

```c
// 改进的ZUPT检测阈值 - 多级检测
#define TH_acc_STRICT				(3*0.542*0.001)//严格ZUPT检测的加表阈值...g (完全静止)
#define TH_acc_LOOSE				(8*0.542*0.001)//宽松ZUPT检测的加表阈值...g (准静止)
#define TH_gyr_STRICT				(3*0.013)//严格ZUPT检测的陀螺阈值...deg/s
#define TH_gyr_LOOSE				(10*0.013)//宽松ZUPT检测的陀螺阈值...deg/s

// ZUPT状态级别定义
#define ZUPT_LEVEL_NONE				0    // 运动状态
#define ZUPT_LEVEL_QUASI_STATIC		1    // 准静止状态
#define ZUPT_LEVEL_STATIC			2    // 完全静止状态
```

**文件修改：** `NAV/nav_type.h`

```c
unsigned char  ZUPT_level;	//ZUPT级别: 0-运动, 1-准静止, 2-完全静止
```

**文件修改：** `NAV/nav_imu.c`

```c
//改进的多级ZUPT检测判断条件
double acc_std = sqrt(acc_std2);
double gyr_std = sqrt(gyr_std2);

if (acc_std < TH_acc_STRICT && gyr_std < TH_gyr_STRICT)
{
    // 完全静止状态
    NAV_Data_Full_p->ZUPT_flag = RETURN_SUCESS;
    NAV_Data_Full_p->ZUPT_level = ZUPT_LEVEL_STATIC;
}
else if (acc_std < TH_acc_LOOSE && gyr_std < TH_gyr_LOOSE)
{
    // 准静止状态
    NAV_Data_Full_p->ZUPT_flag = RETURN_SUCESS;
    NAV_Data_Full_p->ZUPT_level = ZUPT_LEVEL_QUASI_STATIC;
}
else
{
    // 运动状态
    NAV_Data_Full_p->ZUPT_flag = RETURN_FAIL;
    NAV_Data_Full_p->ZUPT_level = ZUPT_LEVEL_NONE;
}
```

#### 2.2 改进的航向角约束机制

**文件修改：** `NAV/nav_const.h`

```c
// 改进的航向角约束触发条件
#define ZUPT_DampingHeading_Size_MIN    10//最小触发周期 - 2秒
#define ZUPT_DampingHeading_Size_MAX    60//最大触发周期 - 12秒  
```

**文件修改：** `NAV/nav_kf.c`

```c
// 改进：根据ZUPT级别动态调整触发门槛
unsigned int trigger_threshold = ZUPT_DampingHeading_Size_MIN;
if (NAV_Data_Full_p->ZUPT_level == ZUPT_LEVEL_STATIC) {
    trigger_threshold = ZUPT_DampingHeading_Size_MIN;  // 完全静止：快速触发
} else if (NAV_Data_Full_p->ZUPT_level == ZUPT_LEVEL_QUASI_STATIC) {
    trigger_threshold = ZUPT_DampingHeading_Size_MIN * 2;  // 准静止：延迟触发
}

if (NAV_Data_Full_p->ZUPTyaw_ST_Cnt >= trigger_threshold)
{
    ZkPre_ZUPT_heading = NAV_Data_Full_p->SINS.att[2];//首次获取航向角
}
```

#### 2.3 动态观测噪声调整

**文件修改：** `NAV/nav_kf.c`

```c
// 改进：根据ZUPT级别和静止时间动态调整观测噪声
if (NAV_Data_Full_p->ZUPT_level == ZUPT_LEVEL_STATIC) {
    // 完全静止：强约束，小噪声
    double time_factor = (double)NAV_Data_Full_p->ZUPTyaw_ST_Cnt / ZUPT_DampingHeading_Size_MAX;
    if (time_factor > 1.0) time_factor = 1.0;
    R_ZUPT_Heading = (0.1*DEG2RAD)*(0.1*DEG2RAD) * (1.0 - 0.8*time_factor);  // 随时间减小
} else {
    // 准静止：弱约束，大噪声
    R_ZUPT_Heading = (0.5*DEG2RAD)*(0.5*DEG2RAD);
}
```

#### 2.4 改进的过程噪声动态调整

**文件修改：** `NAV/nav_kf.c` 和 `NAV/nav_kf.h`

```c
void Q_MAT_UP_Enhanced(_NAV_Data_Full_t* NAV_Data_Full_p)
{
    // 基础过程噪声设置
    Q_MAT_UP(NAV_Data_Full_p);
    
    // 根据ZUPT状态动态调整过程噪声
    if (NAV_Data_Full_p->ZUPT_flag == RETURN_SUCESS) {
        double reduction_factor = 1.0;
        
        if (NAV_Data_Full_p->ZUPT_level == ZUPT_LEVEL_STATIC) {
            // 完全静止：大幅减小过程噪声
            reduction_factor = 0.1;
        } else if (NAV_Data_Full_p->ZUPT_level == ZUPT_LEVEL_QUASI_STATIC) {
            // 准静止：适度减小过程噪声
            reduction_factor = 0.3;
        }
        
        // 减小航向角过程噪声 (状态索引2)
        NAV_Data_Full_p->KF.Qk[2 + NA * 2] *= reduction_factor;
        
        // 减小速度过程噪声 (状态索引3-5)
        for (int i = 3; i <= 5; i++) {
            NAV_Data_Full_p->KF.Qk[i + NA * i] *= reduction_factor;
        }
    }
}
```

## 3. 预期改进效果

### 3.1 ZUPT检测改进效果
- **提高触发频率**：通过多级检测，增加ZUPT触发机会
- **适应不同场景**：完全静止和准静止状态分别处理
- **减少误触发**：保持检测精度的同时提高灵敏度

### 3.2 航向角约束改进效果
- **快速响应**：从60周期降低到10周期，响应时间从12秒降低到2秒
- **渐进约束**：根据静止时间长短动态调整约束强度
- **减少发散**：在静止状态下有效抑制航向角漂移

### 3.3 过程噪声调整效果
- **动态适应**：根据运动状态调整噪声参数
- **提高精度**：静止状态下减小过程噪声，提高滤波精度
- **保持稳定性**：避免过度约束导致的滤波器不稳定

## 4. 使用建议

### 4.1 参数调优
建议根据实际应用场景调整以下参数：
- ZUPT检测阈值：根据车辆振动特性调整
- 航向角约束强度：根据陀螺仪性能调整
- 过程噪声缩减因子：根据IMU精度调整

### 4.2 测试验证
建议进行以下测试验证改进效果：
- 静止状态长时间测试
- 低速行驶测试
- GNSS信号遮挡测试
- 不同环境条件下的鲁棒性测试

### 4.3 监控指标
建议监控以下关键指标：
- ZUPT触发频率和级别分布
- 航向角约束触发时间
- 北向速度和航向角的发散程度
- 滤波器协方差矩阵的变化

## 5. 总结

本改进方案通过多级ZUPT检测、动态航向角约束、自适应噪声调整等技术手段，有效解决了INS设备在GNSS失锁后北向速度和航向角发散的问题。改进方案保持了系统的兼容性，同时显著提升了导航精度和鲁棒性。
