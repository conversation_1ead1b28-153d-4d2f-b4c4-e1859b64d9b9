.\objects\tcpserver.o: ..\Source\src\TCPServer.c
.\objects\tcpserver.o: ..\Source\inc\TCPServer.h
.\objects\tcpserver.o: ..\bsp\inc\CH395SPI.H
.\objects\tcpserver.o: ..\bsp\inc\CH395INC.H
.\objects\tcpserver.o: ..\bsp\inc\bsp_sys.h
.\objects\tcpserver.o: ..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\tcpserver.o: ..\Library\CMSIS\core_cm4.h
.\objects\tcpserver.o: D:\Program Files\MDK\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\tcpserver.o: ..\Library\CMSIS\core_cmInstr.h
.\objects\tcpserver.o: ..\Library\CMSIS\core_cmFunc.h
.\objects\tcpserver.o: ..\Library\CMSIS\core_cm4_simd.h
.\objects\tcpserver.o: ..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h
.\objects\tcpserver.o: ..\Source\inc\gd32f4xx_libopt.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\tcpserver.o: ..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\tcpserver.o: D:\Program Files\MDK\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\tcpserver.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\tcpserver.o: ..\Library\CMSIS\core_cm4.h
.\objects\tcpserver.o: ..\Source\inc\systick.h
.\objects\tcpserver.o: ..\bsp\inc\CH395CMD.H
.\objects\tcpserver.o: D:\Program Files\MDK\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\tcpserver.o: D:\Program Files\MDK\ARM\ARMCC\Bin\..\include\string.h
