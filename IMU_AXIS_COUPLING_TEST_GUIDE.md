# IMU轴间耦合测试指南

## 快速诊断步骤

### 1. 启用诊断代码
在编译时添加宏定义来启用诊断输出：

**方法1：在Keil MDK中添加宏定义**
- 打开项目设置 (Project -> Options for Target)
- 在 C/C++ 选项卡的 Define 框中添加：`DEBUG_AXIS_COUPLING`

**方法2：在代码中直接定义**
在 `nav_imu.c` 文件开头添加：
```c
#define DEBUG_AXIS_COUPLING
```

### 2. 编译并运行
编译项目并下载到设备，通过串口或调试器观察输出信息。

### 3. 分析诊断输出
诊断代码会输出以下信息：
```
=== IMU AXIS COUPLING DIAGNOSIS ===
Axis Config: [X,Y,Z], Signs: [1,1,1]
Raw IMU: acc[0.1234,0.5678,9.8000], gyro[0.0012,0.0034,0.0056]
After 460 Transform: acc[-0.5678,-0.1234,-9.8000], gyro[-0.0034,-0.0012,-0.0056]
Before Axis Adjust: acc[-0.5678,-0.1234,-9.8000], gyro[-0.0034,-0.0012,-0.0056]
Final Used IMU: acc[-0.5678,-0.1234,-9.8000], gyro[-0.0034,-0.0012,-0.0056]
Current Attitude: Roll=1.23, Pitch=2.34, Yaw=45.67
=====================================
```

## 测试方案

### 测试1：静止基准测试
1. **设备水平静止放置**
2. **观察输出数据**：
   - 加速度计应该接近：`acc[0, 0, 9.8]` (东北天坐标系)
   - 陀螺仪应该接近：`gyro[0, 0, 0]`
   - 如果数值偏差很大，说明轴向映射有问题

### 测试2：单轴运动测试
**俯仰运动测试（绕Y轴）**：
1. 缓慢进行俯仰运动（点头动作）
2. 观察陀螺仪输出，主要响应应该在 `gyro[1]`（Y轴）
3. 如果 `gyro[0]` 或 `gyro[2]` 有较大响应，说明存在轴间耦合

**横滚运动测试（绕X轴）**：
1. 缓慢进行横滚运动（左右倾斜）
2. 观察陀螺仪输出，主要响应应该在 `gyro[0]`（X轴）
3. 如果其他轴有较大响应，说明存在轴间耦合

**航向运动测试（绕Z轴）**：
1. 缓慢进行航向运动（水平转动）
2. 观察陀螺仪输出，主要响应应该在 `gyro[2]`（Z轴）
3. **这是您当前遇到的问题**：水平转动时其他轴也有响应

## 问题分析和解决

### 问题1：IMU460轴向转换错误
**现象**：Raw IMU 到 After 460 Transform 的转换不合理

**当前转换**：
```c
gyro[0] = -gyro_raw[1]  // X = -原始Y
gyro[1] = -gyro_raw[0]  // Y = -原始X  
gyro[2] = -gyro_raw[2]  // Z = -原始Z
```

**可能的修正方案**：
```c
// 方案A：直接映射（无转换）
gyro[0] = gyro_raw[0]
gyro[1] = gyro_raw[1]
gyro[2] = gyro_raw[2]

// 方案B：只改变符号
gyro[0] = -gyro_raw[0]
gyro[1] = -gyro_raw[1]
gyro[2] = -gyro_raw[2]

// 方案C：不同的轴向映射
gyro[0] = gyro_raw[1]   // X = 原始Y
gyro[1] = gyro_raw[0]   // Y = 原始X
gyro[2] = gyro_raw[2]   // Z = 原始Z
```

### 问题2：动态轴向调整配置错误
**检查当前配置**：
- `Axis Config` 应该显示正确的轴向组合
- `Signs` 应该显示正确的符号

**常见配置**：
```c
// 标准配置
Axis = ['X', 'Y', 'Z']
Axis_sign = [1, 1, 1]

// 可能需要的配置
Axis = ['Y', 'X', 'Z']  // 如果X和Y需要交换
Axis_sign = [-1, -1, 1] // 如果X和Y需要反向
```

### 问题3：姿态解算算法过度修正
如果硬件配置正确但仍有耦合，可能是Mahony滤波器参数过大导致过度修正。

## 修正代码示例

### 修正IMU460轴向转换
```c
// 在nav_imu.c中修改
if(E_IMU_MANU_460== NAV_Data_Full_p->memsType)//精准460
{
    // 测试方案A：直接映射
    NAV_Data_Full_p->IMU.gyro[0] = NAV_Data_Full_p->IMU.gyro_raw[0];	
    NAV_Data_Full_p->IMU.gyro[1] = NAV_Data_Full_p->IMU.gyro_raw[1];	
    NAV_Data_Full_p->IMU.gyro[2] = NAV_Data_Full_p->IMU.gyro_raw[2];	
    NAV_Data_Full_p->IMU.acc[0] = NAV_Data_Full_p->IMU.acc_raw[0];	
    NAV_Data_Full_p->IMU.acc[1] = NAV_Data_Full_p->IMU.acc_raw[1];	
    NAV_Data_Full_p->IMU.acc[2] = NAV_Data_Full_p->IMU.acc_raw[2];
    
    NAV_Data_Full_p->IMU.temp_mems = NAV_Data_Full_p->IMU.temp_mems_raw;
}
```

### 设置正确的轴向配置
通过上位机或代码设置：
```c
// 在SetParaBao.c中或通过协议设置
combineData.Axis[0] = 'X';
combineData.Axis[1] = 'Y'; 
combineData.Axis[2] = 'Z';

combineData.Axis_sign[0] = 1;
combineData.Axis_sign[1] = 1;
combineData.Axis_sign[2] = 1;

combineData.Axis_flag = 1;
```

## 测试流程建议

### 第一步：启用诊断并观察当前状态
1. 添加 `DEBUG_AXIS_COUPLING` 宏定义
2. 编译运行，观察诊断输出
3. 记录静止状态下的基准值

### 第二步：单轴运动测试
1. 分别进行俯仰、横滚、航向运动
2. 记录每种运动下的陀螺仪响应
3. 识别轴间耦合的具体模式

### 第三步：尝试修正方案
1. 根据测试结果选择合适的修正方案
2. 修改代码并重新测试
3. 验证修正效果

### 第四步：参数优化
1. 如果硬件配置正确但仍有轻微耦合，调整滤波器参数
2. 进行最终的精度验证测试

请先运行诊断代码，将输出结果发给我，我可以帮您分析具体的问题并提供针对性的解决方案。
