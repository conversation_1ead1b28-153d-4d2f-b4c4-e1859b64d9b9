# IMU轴间耦合问题诊断和解决方案

## 问题现象
水平晃动时，俯仰角和横滚角也发生较大变化，表明存在轴间耦合问题。

## 问题根因分析

### 1. IMU轴向映射配置问题
系统使用动态轴向调整，可能存在配置错误：
- Axis[0], Axis[1], Axis[2] 配置不正确
- Axis_sign[0], Axis_sign[1], Axis_sign[2] 符号设置错误

### 2. IMU460固定轴向转换
代码中对IMU460有固定的轴向转换，可能与实际安装不匹配：
```c
gyro[0] = -gyro_raw[1]  // X轴 = -原始Y轴
gyro[1] = -gyro_raw[0]  // Y轴 = -原始X轴  
gyro[2] = -gyro_raw[2]  // Z轴 = -原始Z轴
```

### 3. 标定矩阵问题
IMU标定矩阵可能包含轴间耦合项，但未正确应用。

## 诊断步骤

### 步骤1：检查当前轴向配置
添加调试代码查看当前配置：

```c
// 在nav_imu.c的IMU_UP函数中添加
printf("Axis Config: [%c,%c,%c], Signs: [%d,%d,%d]\n", 
       NAV_Data_Full_p->Axis[0], NAV_Data_Full_p->Axis[1], NAV_Data_Full_p->Axis[2],
       NAV_Data_Full_p->Axis_sign[0], NAV_Data_Full_p->Axis_sign[1], NAV_Data_Full_p->Axis_sign[2]);

printf("Raw IMU: acc[%.4f,%.4f,%.4f], gyro[%.4f,%.4f,%.4f]\n",
       NAV_Data_Full_p->IMU.acc_raw[0], NAV_Data_Full_p->IMU.acc_raw[1], NAV_Data_Full_p->IMU.acc_raw[2],
       NAV_Data_Full_p->IMU.gyro_raw[0]*RAD2DEG, NAV_Data_Full_p->IMU.gyro_raw[1]*RAD2DEG, NAV_Data_Full_p->IMU.gyro_raw[2]*RAD2DEG);

printf("Used IMU: acc[%.4f,%.4f,%.4f], gyro[%.4f,%.4f,%.4f]\n",
       NAV_Data_Full_p->IMU.acc_use[0], NAV_Data_Full_p->IMU.acc_use[1], NAV_Data_Full_p->IMU.acc_use[2],
       NAV_Data_Full_p->IMU.gyro_use[0]*RAD2DEG, NAV_Data_Full_p->IMU.gyro_use[1]*RAD2DEG, NAV_Data_Full_p->IMU.gyro_use[2]*RAD2DEG);
```

### 步骤2：静止状态基准测试
1. 设备完全静止水平放置
2. 记录各轴输出值，应该接近：
   - acc: [0, 0, 9.8] (东北天坐标系)
   - gyro: [0, 0, 0]

### 步骤3：单轴运动测试
分别进行单轴运动测试：
1. **俯仰运动**：只绕Y轴转动，观察gyro[1]是否为主要响应
2. **横滚运动**：只绕X轴转动，观察gyro[0]是否为主要响应  
3. **航向运动**：只绕Z轴转动，观察gyro[2]是否为主要响应

## 解决方案

### 方案1：修正IMU460轴向转换
如果发现轴向映射错误，修改固定转换：

```c
// 在nav_imu.c中修改IMU460的轴向转换
if(E_IMU_MANU_460== NAV_Data_Full_p->memsType)
{
    // 根据实际测试结果调整轴向映射
    // 示例：如果需要不同的映射关系
    NAV_Data_Full_p->IMU.gyro[0] = NAV_Data_Full_p->IMU.gyro_raw[0];   // 可能需要调整
    NAV_Data_Full_p->IMU.gyro[1] = NAV_Data_Full_p->IMU.gyro_raw[1];   // 可能需要调整
    NAV_Data_Full_p->IMU.gyro[2] = NAV_Data_Full_p->IMU.gyro_raw[2];   // 可能需要调整
    
    NAV_Data_Full_p->IMU.acc[0] = NAV_Data_Full_p->IMU.acc_raw[0];     // 可能需要调整
    NAV_Data_Full_p->IMU.acc[1] = NAV_Data_Full_p->IMU.acc_raw[1];     // 可能需要调整
    NAV_Data_Full_p->IMU.acc[2] = NAV_Data_Full_p->IMU.acc_raw[2];     // 可能需要调整
}
```

### 方案2：启用IMU标定矩阵
如果有标定数据，启用标定功能：

```c
// 在nav_imu.c中启用标定
if(E_IMU_ATT_IFOG==NAV_Data_Full_p->imuSelect)
{
    Load_Calib_Parms(NAV_Data_Full_p,CombineData_p);
    
    double tmp[3]={0},tmp2[3]={0};
    
    // 陀螺仪标定
    tmp[0]=NAV_Data_Full_p->IMU.gyro_use[0]*RAD2DEG;
    tmp[1]=NAV_Data_Full_p->IMU.gyro_use[1]*RAD2DEG;
    tmp[2]=NAV_Data_Full_p->IMU.gyro_use[2]*RAD2DEG;
    nav_calib(tmp, &NAV_Data_Full_p->gyroCalib, tmp2);
    NAV_Data_Full_p->IMU.gyro_use[0] = tmp2[0]*DEG2RAD;
    NAV_Data_Full_p->IMU.gyro_use[1] = tmp2[1]*DEG2RAD;
    NAV_Data_Full_p->IMU.gyro_use[2] = tmp2[2]*DEG2RAD;
    
    // 加速度计标定
    memset(tmp,0,sizeof(tmp));
    memset(tmp2,0,sizeof(tmp2));
    tmp[0]=NAV_Data_Full_p->IMU.acc_use[0];
    tmp[1]=NAV_Data_Full_p->IMU.acc_use[1];
    tmp[2]=NAV_Data_Full_p->IMU.acc_use[2];
    nav_calib(tmp, &NAV_Data_Full_p->accCalib, tmp2);
    NAV_Data_Full_p->IMU.acc_use[0] = tmp2[0];
    NAV_Data_Full_p->IMU.acc_use[1] = tmp2[1];
    NAV_Data_Full_p->IMU.acc_use[2] = tmp2[2];
}
```

### 方案3：调整Mahony滤波器参数
如果轴间耦合是由于姿态解算算法造成的，调整Mahony滤波器参数：

```c
// 在nav_mahony.c中调整参数
void SetPidParmByMotion(_NAV_Data_Full_t* NAV_Data_Full_p, _NAV_MAHONY_t* pMahony)
{
    // 降低Kp和Ki值，减少过度修正
    if(NAV_Data_Full_p->ZUPT_flag == RETURN_SUCESS)
    {
        // 静止状态：使用较小的参数
        pMahony->Kp = 0.5;  // 原来可能是2.0
        pMahony->Ki = 0.1;  // 原来可能是0.5
    }
    else
    {
        // 运动状态：使用正常参数
        pMahony->Kp = 1.0;
        pMahony->Ki = 0.2;
    }
}
```

## 推荐的测试流程

### 1. 立即测试
先添加诊断代码，观察当前轴向配置和数据流：

### 2. 静态测试
- 设备水平静止，记录基准值
- 确认重力方向和各轴零偏

### 3. 动态测试  
- 单轴运动测试，确认轴向映射正确性
- 多轴运动测试，检查轴间耦合程度

### 4. 参数调优
根据测试结果调整：
- IMU轴向映射
- 标定矩阵参数
- 姿态滤波器参数

## 常见轴向配置

### 标准配置（东北天坐标系）
- X轴：东向（车辆右侧）
- Y轴：北向（车辆前进方向）  
- Z轴：天向（车辆上方）

### IMU460可能的正确配置
根据测试结果，可能需要的配置：
```c
// 示例配置，需要根据实际测试调整
Axis = ['X', 'Y', 'Z']
Axis_sign = [1, 1, 1]  // 或者 [-1, -1, -1] 等
```

请先运行诊断代码，获取当前配置信息，然后我们可以根据具体数据进一步分析和调整。
