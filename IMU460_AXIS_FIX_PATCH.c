/*
 * IMU460轴间耦合修正补丁
 * 
 * 使用方法：
 * 1. 将此文件中的代码替换nav_imu.c中对应的IMU460处理部分
 * 2. 根据实际测试结果选择合适的修正方案
 * 3. 编译测试验证效果
 */

// 方案1：直接映射（无轴向转换）
// 适用于：IMU460的物理安装轴向与期望的导航轴向一致的情况
void IMU460_Fix_Plan_A(_NAV_Data_Full_t* NAV_Data_Full_p)
{
    if(E_IMU_MANU_460== NAV_Data_Full_p->memsType)//精准460
    {
        // 直接映射，不进行轴向转换
        NAV_Data_Full_p->IMU.gyro[0] = NAV_Data_Full_p->IMU.gyro_raw[0];	
        NAV_Data_Full_p->IMU.gyro[1] = NAV_Data_Full_p->IMU.gyro_raw[1];	
        NAV_Data_Full_p->IMU.gyro[2] = NAV_Data_Full_p->IMU.gyro_raw[2];	
        NAV_Data_Full_p->IMU.acc[0] = NAV_Data_Full_p->IMU.acc_raw[0];	
        NAV_Data_Full_p->IMU.acc[1] = NAV_Data_Full_p->IMU.acc_raw[1];	
        NAV_Data_Full_p->IMU.acc[2] = NAV_Data_Full_p->IMU.acc_raw[2];

        NAV_Data_Full_p->IMU.temp_mems = NAV_Data_Full_p->IMU.temp_mems_raw;
    }
}

// 方案2：只改变符号（保持轴向，改变方向）
// 适用于：轴向正确但方向相反的情况
void IMU460_Fix_Plan_B(_NAV_Data_Full_t* NAV_Data_Full_p)
{
    if(E_IMU_MANU_460== NAV_Data_Full_p->memsType)//精准460
    {
        // 保持轴向，只改变符号
        NAV_Data_Full_p->IMU.gyro[0] = -NAV_Data_Full_p->IMU.gyro_raw[0];	
        NAV_Data_Full_p->IMU.gyro[1] = -NAV_Data_Full_p->IMU.gyro_raw[1];	
        NAV_Data_Full_p->IMU.gyro[2] = -NAV_Data_Full_p->IMU.gyro_raw[2];	
        NAV_Data_Full_p->IMU.acc[0] = -NAV_Data_Full_p->IMU.acc_raw[0];	
        NAV_Data_Full_p->IMU.acc[1] = -NAV_Data_Full_p->IMU.acc_raw[1];	
        NAV_Data_Full_p->IMU.acc[2] = -NAV_Data_Full_p->IMU.acc_raw[2];

        NAV_Data_Full_p->IMU.temp_mems = NAV_Data_Full_p->IMU.temp_mems_raw;
    }
}

// 方案3：X和Y轴交换（不改变符号）
// 适用于：X和Y轴需要交换的情况
void IMU460_Fix_Plan_C(_NAV_Data_Full_t* NAV_Data_Full_p)
{
    if(E_IMU_MANU_460== NAV_Data_Full_p->memsType)//精准460
    {
        // X和Y轴交换
        NAV_Data_Full_p->IMU.gyro[0] = NAV_Data_Full_p->IMU.gyro_raw[1];	// X = 原始Y
        NAV_Data_Full_p->IMU.gyro[1] = NAV_Data_Full_p->IMU.gyro_raw[0];	// Y = 原始X
        NAV_Data_Full_p->IMU.gyro[2] = NAV_Data_Full_p->IMU.gyro_raw[2];	// Z = 原始Z
        NAV_Data_Full_p->IMU.acc[0] = NAV_Data_Full_p->IMU.acc_raw[1];	
        NAV_Data_Full_p->IMU.acc[1] = NAV_Data_Full_p->IMU.acc_raw[0];	
        NAV_Data_Full_p->IMU.acc[2] = NAV_Data_Full_p->IMU.acc_raw[2];

        NAV_Data_Full_p->IMU.temp_mems = NAV_Data_Full_p->IMU.temp_mems_raw;
    }
}

// 方案4：X和Y轴交换并改变符号
// 适用于：X和Y轴需要交换且方向相反的情况
void IMU460_Fix_Plan_D(_NAV_Data_Full_t* NAV_Data_Full_p)
{
    if(E_IMU_MANU_460== NAV_Data_Full_p->memsType)//精准460
    {
        // X和Y轴交换并改变符号
        NAV_Data_Full_p->IMU.gyro[0] = -NAV_Data_Full_p->IMU.gyro_raw[1];	// X = -原始Y
        NAV_Data_Full_p->IMU.gyro[1] = -NAV_Data_Full_p->IMU.gyro_raw[0];	// Y = -原始X
        NAV_Data_Full_p->IMU.gyro[2] = NAV_Data_Full_p->IMU.gyro_raw[2];	// Z = 原始Z
        NAV_Data_Full_p->IMU.acc[0] = -NAV_Data_Full_p->IMU.acc_raw[1];	
        NAV_Data_Full_p->IMU.acc[1] = -NAV_Data_Full_p->IMU.acc_raw[0];	
        NAV_Data_Full_p->IMU.acc[2] = NAV_Data_Full_p->IMU.acc_raw[2];

        NAV_Data_Full_p->IMU.temp_mems = NAV_Data_Full_p->IMU.temp_mems_raw;
    }
}

// 方案5：当前方案但只改变Z轴符号
// 适用于：当前X、Y轴映射正确，但Z轴方向错误的情况
void IMU460_Fix_Plan_E(_NAV_Data_Full_t* NAV_Data_Full_p)
{
    if(E_IMU_MANU_460== NAV_Data_Full_p->memsType)//精准460
    {
        // 保持当前X、Y轴映射，只改变Z轴符号
        NAV_Data_Full_p->IMU.gyro[0] = -NAV_Data_Full_p->IMU.gyro_raw[1];	
        NAV_Data_Full_p->IMU.gyro[1] = -NAV_Data_Full_p->IMU.gyro_raw[0];	
        NAV_Data_Full_p->IMU.gyro[2] = NAV_Data_Full_p->IMU.gyro_raw[2];	// 改变：Z = 原始Z（不取负号）
        NAV_Data_Full_p->IMU.acc[0] = -NAV_Data_Full_p->IMU.acc_raw[1];	
        NAV_Data_Full_p->IMU.acc[1] = -NAV_Data_Full_p->IMU.acc_raw[0];	
        NAV_Data_Full_p->IMU.acc[2] = NAV_Data_Full_p->IMU.acc_raw[2];    // 改变：Z = 原始Z（不取负号）

        NAV_Data_Full_p->IMU.temp_mems = NAV_Data_Full_p->IMU.temp_mems_raw;
    }
}

/*
 * 使用指南：
 * 
 * 1. 首先运行诊断代码，观察当前的轴向映射情况
 * 
 * 2. 进行单轴运动测试：
 *    - 俯仰运动：应该主要影响gyro[1]（Y轴）
 *    - 横滚运动：应该主要影响gyro[0]（X轴）
 *    - 航向运动：应该主要影响gyro[2]（Z轴）
 * 
 * 3. 根据测试结果选择合适的方案：
 *    - 如果当前轴向完全错误：尝试方案A
 *    - 如果轴向正确但方向相反：尝试方案B
 *    - 如果X和Y需要交换：尝试方案C或D
 *    - 如果只有Z轴有问题：尝试方案E
 * 
 * 4. 替换nav_imu.c中的对应代码段：
 *    找到 "if(E_IMU_MANU_460== NAV_Data_Full_p->memsType)" 部分
 *    用选择的方案替换其中的轴向转换代码
 * 
 * 5. 编译测试，验证修正效果
 * 
 * 注意：每次只尝试一个方案，测试后再决定是否需要调整
 */

// 推荐的测试顺序：
// 1. 方案A（直接映射）- 最简单，先试试看
// 2. 方案E（只改Z轴）- 如果当前X、Y轴响应正确
// 3. 方案C（X、Y交换）- 如果X、Y轴响应相反
// 4. 其他方案根据具体情况选择
